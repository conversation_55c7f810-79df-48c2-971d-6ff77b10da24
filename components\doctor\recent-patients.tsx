"use client"

import { useEffect, useState } from "react"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import type { Database } from "@/types/supabase"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Loader2, User, Phone, MessageSquare, Eye } from "lucide-react"
import Link from "next/link"

interface PatientData {
  id: string
  full_name: string
  email: string
  phone_number?: string
  avatar_url?: string
  gender?: string
  date_of_birth?: string
  linked_at: string
  last_accessed?: string
  patient_info?: {
    diagnosis?: string
    cancer_type?: string
    treatment_status?: string
  }
}

interface RecentPatientsProps {
  doctorId: string
  showAll?: boolean
}

export function RecentPatients({ doctorId, showAll = false }: RecentPatientsProps) {
  const [patients, setPatients] = useState<PatientData[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createClientComponentClient<Database>()

  useEffect(() => {
    async function fetchRecentPatients() {
      try {
        // Lấy danh sách bệnh nhân đã liên kết với bác sĩ
        const query = supabase
          .from("doctor_patient_links")
          .select(`
            patient_id,
            linked_at,
            users!doctor_patient_links_patient_id_fkey (
              id,
              full_name,
              email,
              phone_number,
              avatar_url,
              gender,
              date_of_birth
            ),
            patients (
              diagnosis,
              cancer_type,
              treatment_status
            )
          `)
          .eq("doctor_id", doctorId)
          .eq("status", "approved")
          .order("linked_at", { ascending: false })

        if (!showAll) {
          query.limit(5)
        }

        const { data: linkedPatients, error } = await query

        if (error) {
          console.error("Error fetching patients:", error)
          return
        }

        if (!linkedPatients) {
          setPatients([])
          return
        }

        // Lấy thông tin truy cập gần đây
        const patientIds = linkedPatients.map(p => p.patient_id)
        const { data: accessLogs } = await supabase
          .from("medical_record_access_logs")
          .select("patient_id, accessed_at")
          .eq("doctor_id", doctorId)
          .in("patient_id", patientIds)
          .order("accessed_at", { ascending: false })

        // Tạo map để tra cứu thời gian truy cập gần nhất
        const lastAccessMap = new Map()
        accessLogs?.forEach(log => {
          if (!lastAccessMap.has(log.patient_id)) {
            lastAccessMap.set(log.patient_id, log.accessed_at)
          }
        })

        // Kết hợp dữ liệu
        const patientsData: PatientData[] = linkedPatients.map(link => ({
          id: link.patient_id,
          full_name: link.users?.full_name || "Không có tên",
          email: link.users?.email || "",
          phone_number: link.users?.phone_number,
          avatar_url: link.users?.avatar_url,
          gender: link.users?.gender,
          date_of_birth: link.users?.date_of_birth,
          linked_at: link.linked_at,
          last_accessed: lastAccessMap.get(link.patient_id),
          patient_info: {
            diagnosis: link.patients?.diagnosis,
            cancer_type: link.patients?.cancer_type,
            treatment_status: link.patients?.treatment_status,
          }
        }))

        setPatients(patientsData)
      } catch (error) {
        console.error("Error fetching recent patients:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchRecentPatients()
  }, [doctorId, showAll, supabase])

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map(n => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2)
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("vi-VN", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric"
    })
  }

  const getStatusColor = (status?: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800"
      case "completed":
        return "bg-blue-100 text-blue-800"
      case "on_hold":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {showAll ? "Tất cả bệnh nhân" : "Bệnh nhân gần đây"}
          </CardTitle>
          <CardDescription>
            {showAll ? "Danh sách tất cả bệnh nhân đã liên kết" : "5 bệnh nhân được truy cập gần đây nhất"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          {showAll ? "Tất cả bệnh nhân" : "Bệnh nhân gần đây"}
        </CardTitle>
        <CardDescription>
          {showAll ? "Danh sách tất cả bệnh nhân đã liên kết" : "5 bệnh nhân được truy cập gần đây nhất"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {patients.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <User className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Chưa có bệnh nhân nào được liên kết</p>
          </div>
        ) : (
          <div className="space-y-4">
            {patients.map((patient) => (
              <div key={patient.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                <div className="flex items-center space-x-4">
                  <Avatar>
                    <AvatarImage src={patient.avatar_url} />
                    <AvatarFallback>{getInitials(patient.full_name)}</AvatarFallback>
                  </Avatar>
                  <div className="space-y-1">
                    <p className="font-medium">{patient.full_name}</p>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      {patient.patient_info?.cancer_type && (
                        <Badge variant="outline" className="text-xs">
                          {patient.patient_info.cancer_type}
                        </Badge>
                      )}
                      {patient.patient_info?.treatment_status && (
                        <Badge className={`text-xs ${getStatusColor(patient.patient_info.treatment_status)}`}>
                          {patient.patient_info.treatment_status}
                        </Badge>
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Liên kết: {formatDate(patient.linked_at)}
                      {patient.last_accessed && (
                        <span> • Truy cập: {formatDate(patient.last_accessed)}</span>
                      )}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button size="sm" variant="outline" asChild>
                    <Link href={`/messages?recipient=${patient.id}`}>
                      <MessageSquare className="h-4 w-4" />
                    </Link>
                  </Button>
                  <Button size="sm" variant="outline" asChild>
                    <Link href={`/doctor/patients/${patient.id}`}>
                      <Eye className="h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            ))}
            {!showAll && patients.length >= 5 && (
              <div className="text-center pt-4">
                <Button variant="outline" asChild>
                  <Link href="/doctor/patient-links">
                    Xem tất cả bệnh nhân
                  </Link>
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
