"use client"

import { useState } from "react"
import { format } from "date-fns"
import { vi } from "date-fns/locale"
import { CalendarIcon, Clock, MapPin, User } from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import type { Consultation } from "@/types"

interface UpcomingAppointmentsProps {
  appointments?: Partial<Consultation>[]
}

export function UpcomingAppointments({ appointments }: UpcomingAppointmentsProps) {
  const [viewAll, setViewAll] = useState(false)

  // Default appointments if none provided
  const defaultAppointments: Partial<Consultation>[] = [
    {
      id: "1",
      title: "Tư vấn định kỳ",
      scheduled_date: new Date().toISOString(),
      status: "scheduled",
      meeting_link: "https://meet.google.com/abc-defg-hij",
    },
    {
      id: "2",
      title: "<PERSON><PERSON><PERSON> gi<PERSON> kết quả xét nghiệm",
      scheduled_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
      status: "scheduled",
      meeting_link: "https://meet.google.com/klm-nopq-rst",
    },
    {
      id: "3",
      title: "Tư vấn dinh dưỡng",
      scheduled_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      status: "scheduled",
      meeting_link: "https://meet.google.com/uvw-xyz-123",
    },
  ]

  const displayAppointments = appointments || defaultAppointments
  const visibleAppointments = viewAll ? displayAppointments : displayAppointments.slice(0, 2)

  return (
    <Card>
      <CardHeader>
        <CardTitle>Lịch hẹn sắp tới</CardTitle>
        <CardDescription>Các cuộc hẹn tư vấn sắp tới của bạn</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {visibleAppointments.length > 0 ? (
          visibleAppointments.map((appointment, index) => (
            <div key={index} className="flex flex-col space-y-3 rounded-lg border p-3">
              <div className="flex items-center justify-between">
                <div className="font-medium">{appointment.title}</div>
                <Badge variant={appointment.status === "scheduled" ? "outline" : "secondary"}>
                  {appointment.status === "scheduled" ? "Đã lên lịch" : "Đang chờ"}
                </Badge>
              </div>
              <div className="flex flex-col space-y-1 text-sm text-muted-foreground">
                <div className="flex items-center">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {appointment.scheduled_date
                    ? format(new Date(appointment.scheduled_date), "EEEE, dd/MM/yyyy", { locale: vi })
                    : "Chưa xác định"}
                </div>
                <div className="flex items-center">
                  <Clock className="mr-2 h-4 w-4" />
                  {appointment.scheduled_date
                    ? format(new Date(appointment.scheduled_date), "HH:mm", { locale: vi })
                    : "Chưa xác định"}
                </div>
                <div className="flex items-center">
                  <User className="mr-2 h-4 w-4" />
                  <div className="flex items-center">
                    <Avatar className="h-6 w-6 mr-2">
                      <AvatarImage src="/caring-doctor.png" />
                      <AvatarFallback>BS</AvatarFallback>
                    </Avatar>
                    <span>Bác sĩ Nguyễn Văn A</span>
                  </div>
                </div>
                <div className="flex items-center">
                  <MapPin className="mr-2 h-4 w-4" />
                  {appointment.meeting_link ? "Tư vấn trực tuyến" : "Tư vấn trực tiếp"}
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-6 text-muted-foreground">Bạn không có lịch hẹn nào sắp tới</div>
        )}
      </CardContent>
      {displayAppointments.length > 2 && (
        <CardFooter>
          <Button variant="outline" className="w-full" onClick={() => setViewAll(!viewAll)}>
            {viewAll ? "Thu gọn" : `Xem tất cả (${displayAppointments.length})`}
          </Button>
        </CardFooter>
      )}
    </Card>
  )
}
