import { redirect } from "next/navigation"
import { createClient } from "@/lib/supabase/server"
import { DoctorConsultationList } from "@/components/consultation/doctor-consultation-list"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default async function DoctorConsultationsPage() {
  const supabase = createClient()

  // Kiểm tra phiên đăng nhập
  const {
    data: { session },
  } = await supabase.auth.getSession()

  if (!session) {
    redirect("/login")
  }

  // Lấy thông tin người dùng
  const { data: userData } = await supabase.from("users").select("*").eq("id", session.user.id).single()

  // Nếu không phải bác sĩ, chuyển hướng về trang dashboard
  if (userData?.role !== "medical_staff") {
    redirect("/dashboard")
  }

  return (
    <div className="container mx-auto py-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">Quản lý lịch tư vấn</h1>
        <p className="text-muted-foreground mt-2">Xem và quản lý các buổi tư vấn được phân công cho bạn.</p>
      </div>

      <Tabs defaultValue="upcoming" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="upcoming">Sắp tới</TabsTrigger>
          <TabsTrigger value="past">Đã hoàn thành</TabsTrigger>
          <TabsTrigger value="all">Tất cả</TabsTrigger>
        </TabsList>
        <TabsContent value="upcoming">
          <Card>
            <CardHeader>
              <CardTitle>Lịch tư vấn sắp tới</CardTitle>
              <CardDescription>Danh sách các buổi tư vấn sắp diễn ra trong thời gian tới.</CardDescription>
            </CardHeader>
            <CardContent>
              <DoctorConsultationList userId={session.user.id} filter="upcoming" />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="past">
          <Card>
            <CardHeader>
              <CardTitle>Lịch tư vấn đã hoàn thành</CardTitle>
              <CardDescription>Danh sách các buổi tư vấn đã diễn ra.</CardDescription>
            </CardHeader>
            <CardContent>
              <DoctorConsultationList userId={session.user.id} filter="past" />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="all">
          <Card>
            <CardHeader>
              <CardTitle>Tất cả lịch tư vấn</CardTitle>
              <CardDescription>Danh sách tất cả các buổi tư vấn của bạn.</CardDescription>
            </CardHeader>
            <CardContent>
              <DoctorConsultationList userId={session.user.id} filter="all" />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
