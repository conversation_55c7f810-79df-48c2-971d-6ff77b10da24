"use client"

import type React from "react"
import { cn } from "@/lib/utils"
import { useEffect, useState, useRef } from "react"

interface AnimatedGradientProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  intensity?: "subtle" | "medium" | "strong"
  speed?: "slow" | "medium" | "fast"
  colors?: string[]
  blur?: number
  opacity?: number
  className?: string
}

export function AnimatedGradient({
  children,
  className,
  intensity = "medium",
  speed = "medium",
  colors = ["#1e40af", "#1e3a8a", "#3b82f6"],
  blur = 100,
  opacity = 0.15,
  ...props
}: AnimatedGradientProps) {
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [isHovering, setIsHovering] = useState(false)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  // Intensity settings
  const intensityMap = {
    subtle: "bg-gradient-to-r from-blue-50/30 via-blue-100/30 to-blue-50/30",
    medium: "bg-gradient-to-r from-blue-100/40 via-blue-200/40 to-blue-100/40",
    strong: "bg-gradient-to-r from-blue-200/50 via-blue-300/50 to-blue-200/50",
  }

  // Speed settings
  const speedMap = {
    slow: "animate-gradient-x-slow",
    medium: "animate-gradient-x",
    fast: "animate-gradient-x-fast",
  }

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    let animationFrameId: number
    const hue = 0
    let particles: {
      x: number
      y: number
      size: number
      color: string
      speedX: number
      speedY: number
    }[] = []

    const resizeCanvas = () => {
      const { width, height } = canvas.getBoundingClientRect()
      const dpr = window.devicePixelRatio || 1
      canvas.width = width * dpr
      canvas.height = height * dpr
      ctx.scale(dpr, dpr)
    }

    const createParticles = () => {
      particles = []
      const numberOfParticles = Math.floor((canvas.width * canvas.height) / 15000)

      for (let i = 0; i < numberOfParticles; i++) {
        const size = Math.random() * 2 + 0.1
        const colorIndex = Math.floor(Math.random() * colors.length)
        particles.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          size,
          color: colors[colorIndex],
          speedX: ((Math.random() - 0.5) * 4) / 10,
          speedY: ((Math.random() - 0.5) * 4) / 10,
        })
      }
    }

    const drawParticles = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      particles.forEach((particle, i) => {
        particle.x += particle.speedX
        particle.y += particle.speedY

        if (particle.x < 0) particle.x = canvas.width
        if (particle.x > canvas.width) particle.x = 0
        if (particle.y < 0) particle.y = canvas.height
        if (particle.y > canvas.height) particle.y = 0

        ctx.beginPath()
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
        ctx.fillStyle = particle.color
        ctx.globalAlpha = opacity
        ctx.fill()
      })

      ctx.filter = `blur(${blur}px)`

      animationFrameId = requestAnimationFrame(drawParticles)
    }

    const handleResize = () => {
      resizeCanvas()
      createParticles()
    }

    window.addEventListener("resize", handleResize)
    resizeCanvas()
    createParticles()
    drawParticles()

    return () => {
      window.removeEventListener("resize", handleResize)
      cancelAnimationFrame(animationFrameId)
    }
  }, [colors, blur, opacity])

  useEffect(() => {
    const style = document.createElement("style")
    style.innerHTML = `
      @keyframes gradient-x-slow {
        0%, 100% { background-position: 0% 50% }
        50% { background-position: 100% 50% }
      }
      @keyframes gradient-x {
        0%, 100% { background-position: 0% 50% }
        50% { background-position: 100% 50% }
      }
      @keyframes gradient-x-fast {
        0%, 100% { background-position: 0% 50% }
        50% { background-position: 100% 50% }
      }
      .animate-gradient-x-slow {
        animation: gradient-x-slow 15s ease infinite;
        background-size: 200% 200%;
      }
      .animate-gradient-x {
        animation: gradient-x 10s ease infinite;
        background-size: 200% 200%;
      }
      .animate-gradient-x-fast {
        animation: gradient-x-fast 5s ease infinite;
        background-size: 200% 200%;
      }
    `
    document.head.appendChild(style)
    return () => {
      document.head.removeChild(style)
    }
  }, [])

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isHovering) return
    const rect = e.currentTarget.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top
    setPosition({ x, y })
  }

  return (
    <div
      className={cn("relative overflow-hidden rounded-lg", intensityMap[intensity], speedMap[speed], className)}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      onMouseMove={handleMouseMove}
      {...props}
    >
      {isHovering && (
        <div
          className="absolute pointer-events-none bg-white/10 rounded-full blur-xl opacity-70 transition-transform duration-500"
          style={{
            width: "150px",
            height: "150px",
            left: `${position.x - 75}px`,
            top: `${position.y - 75}px`,
            transform: "translate(-50%, -50%)",
          }}
        />
      )}
      <canvas ref={canvasRef} className="absolute inset-0 w-full h-full object-cover" style={{ zIndex: -1 }} />
      {children}
    </div>
  )
}
