"use client"

import Link from "next/link"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
  Users,
  Calendar,
  MessageSquare,
  FileText,
  Share2,
  Stethoscope,
  ClipboardList,
  UserPlus
} from "lucide-react"

export function DoctorQuickLinks() {
  const quickLinks = [
    {
      title: "Quản lý bệnh nhân",
      icon: Users,
      href: "/doctor/patient-links",
      color: "text-blue-500",
    },
    {
      title: "<PERSON><PERSON>ch tư vấn",
      icon: Calendar,
      href: "/consultations/doctor",
      color: "text-blue-500",
    },
    {
      title: "<PERSON><PERSON><PERSON> cầu tư vấn",
      icon: ClipboardList,
      href: "/advisory",
      color: "text-blue-500",
    },
    {
      title: "Hội chẩn y khoa",
      icon: MessageSquare,
      href: "/consultations/staff-request",
      color: "text-blue-500",
    },
    {
      title: "<PERSON><PERSON> s<PERSON> đ<PERSON> chia sẻ",
      icon: <PERSON><PERSON><PERSON>,
      href: "/shared-records",
      color: "text-blue-500",
    },
    {
      title: "Nhắn tin",
      icon: MessageSquare,
      href: "/messages",
      color: "text-blue-500",
    },
  ]

  return (
    <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
      {quickLinks.map((link, index) => {
        const Icon = link.icon
        return (
          <Link key={index} href={link.href} className="group">
            <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
              <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                <Icon className={`mb-2 h-8 w-8 ${link.color}`} />
                <p className="font-medium">{link.title}</p>
              </CardContent>
            </Card>
          </Link>
        )
      })}
    </div>
  )
}
