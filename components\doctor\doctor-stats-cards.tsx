"use client"

import { useEffect, useState } from "react"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import type { Database } from "@/types/supabase"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Users, Calendar, Clock, MessageSquare, Activity, AlertCircle } from "lucide-react"

interface StatsData {
  totalPatients: number
  todayConsultations: number
  pendingAdvisories: number
  thisWeekConsultations: number
  pendingStaffConsultations: number
  recentActivity: number
}

interface DoctorStatsCardsProps {
  doctorId: string
}

export function DoctorStatsCards({ doctorId }: DoctorStatsCardsProps) {
  const [stats, setStats] = useState<StatsData>({
    totalPatients: 0,
    todayConsultations: 0,
    pendingAdvisories: 0,
    thisWeekConsultations: 0,
    pendingStaffConsultations: 0,
    recentActivity: 0,
  })
  const [loading, setLoading] = useState(true)
  const supabase = createClientComponentClient<Database>()

  useEffect(() => {
    async function fetchStats() {
      try {
        const today = new Date()
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
        const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)

        const startOfWeek = new Date(today)
        startOfWeek.setDate(today.getDate() - today.getDay())
        startOfWeek.setHours(0, 0, 0, 0)

        const endOfWeek = new Date(startOfWeek)
        endOfWeek.setDate(startOfWeek.getDate() + 7)

        // Tổng số bệnh nhân đã liên kết
        const { count: totalPatients } = await supabase
          .from("doctor_patient_links")
          .select("*", { count: "exact", head: true })
          .eq("doctor_id", doctorId)
          .eq("status", "approved")

        // Số lịch tư vấn hôm nay
        const { count: todayConsultations } = await supabase
          .from("patient_advisories")
          .select("*", { count: "exact", head: true })
          .gte("scheduled_start_time", startOfDay.toISOString())
          .lt("scheduled_start_time", endOfDay.toISOString())
          .in("status", ["scheduled", "in_progress"])

        // Số yêu cầu tư vấn đang chờ xử lý (được assign cho bác sĩ này)
        const { count: pendingAdvisories } = await supabase
          .from("advisory_assignments")
          .select("*", { count: "exact", head: true })
          .eq("assigned_staff_id", doctorId)
          .eq("status", "pending")

        // Số lịch tư vấn tuần này
        const { count: thisWeekConsultations } = await supabase
          .from("patient_advisories")
          .select("*", { count: "exact", head: true })
          .gte("scheduled_start_time", startOfWeek.toISOString())
          .lt("scheduled_start_time", endOfWeek.toISOString())
          .in("status", ["scheduled", "in_progress", "completed"])

        // Số hội chẩn đang chờ xử lý
        const { count: pendingStaffConsultations } = await supabase
          .from("staff_consultations")
          .select("*", { count: "exact", head: true })
          .eq("requesting_staff_id", doctorId)
          .eq("status", "pending")

        // Hoạt động gần đây (số bệnh nhân có tương tác trong 7 ngày qua)
        const sevenDaysAgo = new Date(today)
        sevenDaysAgo.setDate(today.getDate() - 7)

        const { count: recentActivity } = await supabase
          .from("medical_record_access_logs")
          .select("*", { count: "exact", head: true })
          .eq("doctor_id", doctorId)
          .gte("accessed_at", sevenDaysAgo.toISOString())

        setStats({
          totalPatients: totalPatients || 0,
          todayConsultations: todayConsultations || 0,
          pendingAdvisories: pendingAdvisories || 0,
          thisWeekConsultations: thisWeekConsultations || 0,
          pendingStaffConsultations: pendingStaffConsultations || 0,
          recentActivity: recentActivity || 0,
        })
      } catch (error) {
        console.error("Error fetching stats:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [doctorId, supabase])

  const statsCards = [
    {
      title: "Tổng bệnh nhân",
      value: stats.totalPatients,
      description: "Bệnh nhân đang theo dõi",
      icon: Users,
      color: "text-blue-600",
    },
    {
      title: "Tư vấn hôm nay",
      value: stats.todayConsultations,
      description: "Lịch hẹn trong ngày",
      icon: Calendar,
      color: "text-green-600",
    },
    {
      title: "Yêu cầu chờ xử lý",
      value: stats.pendingAdvisories,
      description: "Tư vấn cần phản hồi",
      icon: AlertCircle,
      color: "text-orange-600",
    },
    {
      title: "Tư vấn tuần này",
      value: stats.thisWeekConsultations,
      description: "Tổng số buổi tư vấn",
      icon: Clock,
      color: "text-purple-600",
    },
    {
      title: "Hội chẩn chờ",
      value: stats.pendingStaffConsultations,
      description: "Yêu cầu hội chẩn",
      icon: MessageSquare,
      color: "text-red-600",
    },
    {
      title: "Hoạt động gần đây",
      value: stats.recentActivity,
      description: "Truy cập trong 7 ngày",
      icon: Activity,
      color: "text-indigo-600",
    },
  ]

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Đang tải...</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">--</div>
              <p className="text-xs text-muted-foreground">Đang tải dữ liệu...</p>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center text-lg">
          <Activity className="mr-2 h-5 w-5 text-blue-500" />
          Thống kê tổng quan
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2">
          {statsCards.map((stat, index) => {
            const Icon = stat.icon
            return (
              <div key={index} className="flex items-center justify-between rounded-lg border p-3">
                <div className="flex items-center gap-2">
                  <Icon className={`h-4 w-4 ${stat.color}`} />
                  <span className="font-medium">{stat.title}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-lg font-bold text-blue-600">
                    {stat.value}
                  </span>
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
