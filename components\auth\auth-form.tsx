"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Loader2, Mail, Lock, Eye, EyeOff, CheckCircle2 } from "lucide-react"
import { BackgroundGradient } from "@/components/ui/background-gradient"
import { SocialLoginButtons } from "@/components/auth/social-login-buttons"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"

export function AuthForm() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [passwordError, setPasswordError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("signin")
  const [passwordStrength, setPasswordStrength] = useState(0)
  const router = useRouter()
  const supabase = createClient()
  const { toast } = useToast()

  // Đánh giá độ mạnh của mật khẩu
  useEffect(() => {
    if (!password) {
      setPasswordStrength(0)
      return
    }

    let strength = 0
    // Độ dài tối thiểu 8 ký tự
    if (password.length >= 8) strength += 1
    // Có chữ hoa và chữ thường
    if (/[a-z]/.test(password) && /[A-Z]/.test(password)) strength += 1
    // Có số
    if (/[0-9]/.test(password)) strength += 1
    // Có ký tự đặc biệt
    if (/[^a-zA-Z0-9]/.test(password)) strength += 1

    setPasswordStrength(strength)
  }, [password])

  // Kiểm tra mật khẩu nhập lại có khớp không
  useEffect(() => {
    if (confirmPassword && password !== confirmPassword) {
      setPasswordError("Mật khẩu nhập lại không khớp")
    } else {
      setPasswordError(null)
    }
  }, [password, confirmPassword])

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)

    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        setError(error.message)
        return
      }

      toast({
        title: "Đăng nhập thành công",
        description: "Chào mừng bạn quay trở lại OncoCare.VN",
      })

      router.push("/dashboard")
      router.refresh()
    } catch (err) {
      setError("Đã xảy ra lỗi khi đăng nhập")
    } finally {
      setIsLoading(false)
    }
  }

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)
    setSuccessMessage(null)

    // Kiểm tra mật khẩu nhập lại
    if (password !== confirmPassword) {
      setError("Mật khẩu nhập lại không khớp")
      setIsLoading(false)
      return
    }

    // Kiểm tra độ mạnh của mật khẩu
    if (passwordStrength < 3) {
      setError("Mật khẩu không đủ mạnh. Vui lòng sử dụng mật khẩu có chữ hoa, chữ thường, số và ký tự đặc biệt.")
      setIsLoading(false)
      return
    }

    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      })

      if (error) {
        setError(error.message)
        return
      }

      setSuccessMessage("Vui lòng kiểm tra email của bạn để xác nhận đăng ký.")
      toast({
        title: "Đăng ký thành công",
        description: "Vui lòng kiểm tra email của bạn để xác nhận đăng ký.",
      })
    } catch (err) {
      setError("Đã xảy ra lỗi khi đăng ký")
    } finally {
      setIsLoading(false)
    }
  }

  const handleTabChange = (value: string) => {
    setActiveTab(value)
    setError(null)
    setSuccessMessage(null)
    setPasswordError(null)
    if (value === "signin") {
      setConfirmPassword("")
    }
  }

  const getPasswordStrengthText = () => {
    if (!password) return ""
    if (passwordStrength === 0) return "Rất yếu"
    if (passwordStrength === 1) return "Yếu"
    if (passwordStrength === 2) return "Trung bình"
    if (passwordStrength === 3) return "Mạnh"
    return "Rất mạnh"
  }

  const getPasswordStrengthColor = () => {
    if (!password) return "bg-gray-200"
    if (passwordStrength === 0) return "bg-red-500"
    if (passwordStrength === 1) return "bg-orange-500"
    if (passwordStrength === 2) return "bg-yellow-500"
    if (passwordStrength === 3) return "bg-green-500"
    return "bg-emerald-500"
  }

  return (
    <BackgroundGradient className="w-full min-h-screen flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="w-full backdrop-blur-sm bg-background/95 border shadow-xl">
          <CardHeader className="space-y-1">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.5 }}
            >
              <CardTitle className="text-2xl text-center font-bold bg-gradient-to-r from-blue-500 to-blue-700 bg-clip-text text-transparent">
                OncoCare.VN
              </CardTitle>
              <CardDescription className="text-center mt-2">
                Nền tảng kỹ thuật số nâng cao chất lượng chăm sóc và điều trị bệnh nhân Ung thư tại Việt Nam
              </CardDescription>
            </motion.div>
          </CardHeader>
          <CardContent className="pb-3">
            <Tabs defaultValue="signin" value={activeTab} onValueChange={handleTabChange} className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger
                  value="signin"
                  className="data-[state=active]:bg-blue-50 dark:data-[state=active]:bg-blue-950/50"
                >
                  Đăng nhập
                </TabsTrigger>
                <TabsTrigger
                  value="signup"
                  className="data-[state=active]:bg-blue-50 dark:data-[state=active]:bg-blue-950/50"
                >
                  Đăng ký
                </TabsTrigger>
              </TabsList>
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeTab}
                  initial={{ opacity: 0, x: activeTab === "signin" ? -20 : 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: activeTab === "signin" ? 20 : -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <TabsContent value="signin" className="mt-0">
                    <form onSubmit={handleSignIn} className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="email" className="text-sm font-medium">
                          Email
                        </Label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            id="email"
                            type="email"
                            placeholder="<EMAIL>"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            required
                            className="pl-10 focus-visible:ring-blue-500"
                          />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <Label htmlFor="password" className="text-sm font-medium">
                            Mật khẩu
                          </Label>
                          <a href="#" className="text-xs text-blue-600 hover:underline">
                            Quên mật khẩu?
                          </a>
                        </div>
                        <div className="relative">
                          <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            id="password"
                            type={showPassword ? "text" : "password"}
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            required
                            className="pl-10 pr-10 focus-visible:ring-blue-500"
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                          >
                            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </button>
                        </div>
                      </div>
                      <AnimatePresence>
                        {error && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.2 }}
                          >
                            <Alert variant="destructive" className="text-sm py-2">
                              <AlertCircle className="h-4 w-4" />
                              <AlertDescription>{error}</AlertDescription>
                            </Alert>
                          </motion.div>
                        )}
                      </AnimatePresence>
                      <Button
                        type="submit"
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Đang xử lý...
                          </>
                        ) : (
                          "Đăng nhập"
                        )}
                      </Button>
                    </form>
                    <SocialLoginButtons mode="signin" />
                  </TabsContent>
                  <TabsContent value="signup" className="mt-0">
                    <form onSubmit={handleSignUp} className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="signup-email" className="text-sm font-medium">
                          Email
                        </Label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            id="signup-email"
                            type="email"
                            placeholder="<EMAIL>"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            required
                            className="pl-10 focus-visible:ring-blue-500"
                          />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="signup-password" className="text-sm font-medium">
                          Mật khẩu
                        </Label>
                        <div className="relative">
                          <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            id="signup-password"
                            type={showPassword ? "text" : "password"}
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            required
                            className="pl-10 pr-10 focus-visible:ring-blue-500"
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                          >
                            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </button>
                        </div>
                        {password && (
                          <div className="mt-2 space-y-1">
                            <div className="flex justify-between items-center text-xs">
                              <span>Độ mạnh mật khẩu: {getPasswordStrengthText()}</span>
                              <span className={cn(passwordStrength >= 3 ? "text-green-600" : "text-muted-foreground")}>
                                {passwordStrength >= 3 && <CheckCircle2 className="h-3 w-3 inline mr-1" />}
                                {passwordStrength >= 3 ? "An toàn" : "Chưa đủ mạnh"}
                              </span>
                            </div>
                            <div className="h-1 w-full bg-gray-200 rounded-full overflow-hidden">
                              <div
                                className={`h-full ${getPasswordStrengthColor()} transition-all duration-300`}
                                style={{ width: `${(passwordStrength / 4) * 100}%` }}
                              />
                            </div>
                            <ul className="text-xs space-y-1 mt-2 text-muted-foreground">
                              <li className={password.length >= 8 ? "text-green-600" : ""}>
                                {password.length >= 8 ? "✓" : "○"} Ít nhất 8 ký tự
                              </li>
                              <li className={/[a-z]/.test(password) && /[A-Z]/.test(password) ? "text-green-600" : ""}>
                                {/[a-z]/.test(password) && /[A-Z]/.test(password) ? "✓" : "○"} Chữ hoa và chữ thường
                              </li>
                              <li className={/[0-9]/.test(password) ? "text-green-600" : ""}>
                                {/[0-9]/.test(password) ? "✓" : "○"} Ít nhất một số
                              </li>
                              <li className={/[^a-zA-Z0-9]/.test(password) ? "text-green-600" : ""}>
                                {/[^a-zA-Z0-9]/.test(password) ? "✓" : "○"} Ít nhất một ký tự đặc biệt
                              </li>
                            </ul>
                          </div>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="confirm-password" className="text-sm font-medium">
                          Nhập lại mật khẩu
                        </Label>
                        <div className="relative">
                          <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            id="confirm-password"
                            type={showPassword ? "text" : "password"}
                            value={confirmPassword}
                            onChange={(e) => setConfirmPassword(e.target.value)}
                            required
                            className="pl-10 pr-10 focus-visible:ring-blue-500"
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                          >
                            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </button>
                        </div>
                        {passwordError && <p className="text-xs text-red-500 mt-1">{passwordError}</p>}
                      </div>
                      <AnimatePresence>
                        {error && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.2 }}
                          >
                            <Alert variant="destructive" className="text-sm py-2">
                              <AlertCircle className="h-4 w-4" />
                              <AlertDescription>{error}</AlertDescription>
                            </Alert>
                          </motion.div>
                        )}
                        {successMessage && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.2 }}
                          >
                            <Alert
                              variant="default"
                              className="text-sm py-2 bg-green-50 text-green-800 border-green-200"
                            >
                              <CheckCircle2 className="h-4 w-4" />
                              <AlertDescription>{successMessage}</AlertDescription>
                            </Alert>
                          </motion.div>
                        )}
                      </AnimatePresence>
                      <Button
                        type="submit"
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Đang xử lý...
                          </>
                        ) : (
                          "Đăng ký"
                        )}
                      </Button>
                    </form>
                    <SocialLoginButtons mode="signup" />
                  </TabsContent>
                </motion.div>
              </AnimatePresence>
            </Tabs>
          </CardContent>
          <CardFooter className="flex flex-col space-y-2 pt-0">
            <div className="text-xs text-center text-muted-foreground">
              Bằng việc đăng ký, bạn đồng ý với{" "}
              <a href="#" className="text-blue-600 hover:underline">
                Điều khoản dịch vụ
              </a>{" "}
              và{" "}
              <a href="#" className="text-blue-600 hover:underline">
                Chính sách bảo mật
              </a>{" "}
              của chúng tôi.
            </div>
            <div className="text-xs text-center text-muted-foreground">© 2024 OncoCare.VN - Mọi quyền được bảo lưu</div>
          </CardFooter>
        </Card>
      </motion.div>
    </BackgroundGradient>
  )
}
