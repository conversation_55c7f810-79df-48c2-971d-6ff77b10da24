"use client"

import { useEffect, useState } from "react"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import type { Database } from "@/types/supabase"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { Activity, ChevronRight } from "lucide-react"
import Link from "next/link"

interface ProgressData {
  totalPatients: number
  completedConsultations: number
  pendingRequests: number
  thisMonthConsultations: number
}

interface DoctorProgressProps {
  doctorId: string
}

export function DoctorProgress({ doctorId }: DoctorProgressProps) {
  const [progressData, setProgressData] = useState<ProgressData>({
    totalPatients: 0,
    completedConsultations: 0,
    pendingRequests: 0,
    thisMonthConsultations: 0,
  })
  const [loading, setLoading] = useState(true)
  const supabase = createClientComponentClient<Database>()

  useEffect(() => {
    async function fetchProgressData() {
      try {
        const now = new Date()
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0)

        // Tổng số bệnh nhân
        const { count: totalPatients } = await supabase
          .from("doctor_patient_links")
          .select("*", { count: "exact", head: true })
          .eq("doctor_id", doctorId)
          .eq("status", "approved")

        // Số tư vấn đã hoàn thành trong tháng
        const { count: completedConsultations } = await supabase
          .from("patient_advisories")
          .select("*", { count: "exact", head: true })
          .gte("scheduled_start_time", startOfMonth.toISOString())
          .lte("scheduled_start_time", endOfMonth.toISOString())
          .eq("status", "completed")

        // Số yêu cầu đang chờ xử lý
        const { count: pendingRequests } = await supabase
          .from("advisory_assignments")
          .select("*", { count: "exact", head: true })
          .eq("assigned_staff_id", doctorId)
          .eq("status", "pending")

        // Tổng số tư vấn trong tháng
        const { count: thisMonthConsultations } = await supabase
          .from("patient_advisories")
          .select("*", { count: "exact", head: true })
          .gte("scheduled_start_time", startOfMonth.toISOString())
          .lte("scheduled_start_time", endOfMonth.toISOString())

        setProgressData({
          totalPatients: totalPatients || 0,
          completedConsultations: completedConsultations || 0,
          pendingRequests: pendingRequests || 0,
          thisMonthConsultations: thisMonthConsultations || 0,
        })
      } catch (error) {
        console.error("Error fetching progress data:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchProgressData()
  }, [doctorId, supabase])

  if (loading) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center text-lg">
            <Activity className="mr-2 h-5 w-5 text-blue-500" />
            Tiến độ công việc
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Đang tải...</span>
                <span className="font-medium">--</span>
              </div>
              <Progress value={0} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Tính toán phần trăm
  const completionRate = progressData.thisMonthConsultations > 0 
    ? Math.round((progressData.completedConsultations / progressData.thisMonthConsultations) * 100)
    : 0

  const responseRate = progressData.pendingRequests === 0 
    ? 100 
    : Math.max(0, 100 - (progressData.pendingRequests * 10)) // Giả định mỗi yêu cầu chờ giảm 10%

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center text-lg">
          <Activity className="mr-2 h-5 w-5 text-blue-500" />
          Tiến độ công việc
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Tỷ lệ hoàn thành tư vấn</span>
              <span className="font-medium">{completionRate}%</span>
            </div>
            <Progress value={completionRate} className="h-2" />
          </div>

          <div className="space-y-2">
            <p className="font-medium">Tháng này: {progressData.completedConsultations}/{progressData.thisMonthConsultations} tư vấn</p>
            <p className="text-sm text-muted-foreground">
              Đã hoàn thành {progressData.completedConsultations} buổi tư vấn trong tổng số {progressData.thisMonthConsultations} buổi
            </p>
            <div className="flex justify-between text-sm">
              <span>Tỷ lệ phản hồi</span>
              <span className="font-medium">{responseRate}%</span>
            </div>
            <Progress value={responseRate} className="h-2" />
          </div>

          <div className="mt-4 grid grid-cols-2 gap-4 text-center">
            <div className="rounded-lg border p-3">
              <div className="text-2xl font-bold text-blue-600">{progressData.totalPatients}</div>
              <div className="text-sm text-muted-foreground">Bệnh nhân</div>
            </div>
            <div className="rounded-lg border p-3">
              <div className="text-2xl font-bold text-green-600">{progressData.completedConsultations}</div>
              <div className="text-sm text-muted-foreground">Hoàn thành</div>
            </div>
          </div>

          <div className="mt-4 flex justify-end">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/consultations/doctor" className="flex items-center">
                Chi tiết lịch tư vấn
                <ChevronRight className="ml-1 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
