"use client"

import { useEffect, useState } from "react"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import type { Database } from "@/types/supabase"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Loader2, Calendar, Clock, Video, MessageSquare, User } from "lucide-react"
import Link from "next/link"

interface ConsultationData {
  id: string
  title: string
  description: string
  scheduled_start_time: string
  scheduled_end_time?: string
  status: string
  urgency_level: string
  meeting_link?: string
  patient: {
    id: string
    full_name: string
    avatar_url?: string
    cancer_type?: string
  }
}

interface UpcomingConsultationsProps {
  doctorId: string
  showAll?: boolean
}

export function UpcomingConsultations({ doctorId, showAll = false }: UpcomingConsultationsProps) {
  const [consultations, setConsultations] = useState<ConsultationData[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createClientComponentClient<Database>()

  useEffect(() => {
    async function fetchUpcomingConsultations() {
      try {
        const now = new Date().toISOString()
        
        // Lấy các advisory assignments cho bác sĩ này
        const { data: assignments, error: assignmentError } = await supabase
          .from("advisory_assignments")
          .select(`
            advisory_id,
            status,
            assigned_at
          `)
          .eq("assigned_staff_id", doctorId)
          .in("status", ["pending", "accepted"])

        if (assignmentError) {
          console.error("Error fetching assignments:", assignmentError)
          return
        }

        if (!assignments || assignments.length === 0) {
          setConsultations([])
          return
        }

        const advisoryIds = assignments.map(a => a.advisory_id)

        // Lấy thông tin chi tiết của các advisory
        const query = supabase
          .from("patient_advisories")
          .select(`
            id,
            title,
            description,
            scheduled_start_time,
            scheduled_end_time,
            status,
            urgency_level,
            meeting_link,
            patient_id,
            users!patient_advisories_patient_id_fkey (
              id,
              full_name,
              avatar_url
            ),
            patients (
              cancer_type
            )
          `)
          .in("id", advisoryIds)
          .gte("scheduled_start_time", now)
          .order("scheduled_start_time", { ascending: true })

        if (!showAll) {
          query.limit(5)
        }

        const { data: advisories, error } = await query

        if (error) {
          console.error("Error fetching consultations:", error)
          return
        }

        if (!advisories) {
          setConsultations([])
          return
        }

        // Chuyển đổi dữ liệu
        const consultationsData: ConsultationData[] = advisories.map(advisory => ({
          id: advisory.id,
          title: advisory.title,
          description: advisory.description,
          scheduled_start_time: advisory.scheduled_start_time,
          scheduled_end_time: advisory.scheduled_end_time,
          status: advisory.status,
          urgency_level: advisory.urgency_level,
          meeting_link: advisory.meeting_link,
          patient: {
            id: advisory.patient_id,
            full_name: advisory.users?.full_name || "Không có tên",
            avatar_url: advisory.users?.avatar_url,
            cancer_type: advisory.patients?.cancer_type,
          }
        }))

        setConsultations(consultationsData)
      } catch (error) {
        console.error("Error fetching upcoming consultations:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchUpcomingConsultations()
  }, [doctorId, showAll, supabase])

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map(n => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2)
  }

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    return {
      date: date.toLocaleDateString("vi-VN", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric"
      }),
      time: date.toLocaleTimeString("vi-VN", {
        hour: "2-digit",
        minute: "2-digit"
      })
    }
  }

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "high":
        return "bg-red-100 text-red-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      case "low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getUrgencyText = (urgency: string) => {
    switch (urgency) {
      case "high":
        return "Khẩn cấp"
      case "medium":
        return "Trung bình"
      case "low":
        return "Thấp"
      default:
        return "Bình thường"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "scheduled":
        return "bg-blue-100 text-blue-800"
      case "in_progress":
        return "bg-green-100 text-green-800"
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "scheduled":
        return "Đã lên lịch"
      case "in_progress":
        return "Đang diễn ra"
      case "pending":
        return "Chờ xử lý"
      default:
        return status
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {showAll ? "Tất cả lịch tư vấn" : "Lịch tư vấn sắp tới"}
          </CardTitle>
          <CardDescription>
            {showAll ? "Danh sách tất cả lịch tư vấn" : "5 buổi tư vấn sắp diễn ra"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          {showAll ? "Tất cả lịch tư vấn" : "Lịch tư vấn sắp tới"}
        </CardTitle>
        <CardDescription>
          {showAll ? "Danh sách tất cả lịch tư vấn" : "5 buổi tư vấn sắp diễn ra"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {consultations.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Không có lịch tư vấn nào sắp tới</p>
          </div>
        ) : (
          <div className="space-y-4">
            {consultations.map((consultation) => {
              const dateTime = formatDateTime(consultation.scheduled_start_time)
              return (
                <div key={consultation.id} className="p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={consultation.patient.avatar_url} />
                        <AvatarFallback>{getInitials(consultation.patient.full_name)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{consultation.patient.full_name}</p>
                        <p className="text-sm text-muted-foreground">{consultation.title}</p>
                      </div>
                    </div>
                    <div className="flex flex-col items-end space-y-1">
                      <Badge className={`text-xs ${getUrgencyColor(consultation.urgency_level)}`}>
                        {getUrgencyText(consultation.urgency_level)}
                      </Badge>
                      <Badge variant="outline" className={`text-xs ${getStatusColor(consultation.status)}`}>
                        {getStatusText(consultation.status)}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>{dateTime.date}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{dateTime.time}</span>
                      </div>
                      {consultation.patient.cancer_type && (
                        <Badge variant="outline" className="text-xs">
                          {consultation.patient.cancer_type}
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/messages?recipient=${consultation.patient.id}`}>
                          <MessageSquare className="h-4 w-4" />
                        </Link>
                      </Button>
                      {consultation.meeting_link && (
                        <Button size="sm" variant="outline" asChild>
                          <Link href={consultation.meeting_link} target="_blank">
                            <Video className="h-4 w-4" />
                          </Link>
                        </Button>
                      )}
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/advisory/${consultation.id}`}>
                          Xem chi tiết
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              )
            })}
            {!showAll && consultations.length >= 5 && (
              <div className="text-center pt-4">
                <Button variant="outline" asChild>
                  <Link href="/consultations/doctor">
                    Xem tất cả lịch tư vấn
                  </Link>
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
