{
  "name": "application",
  "dockerComposeFile": "docker-compose.yml",
  "service": "application",
  "workspaceFolder": "/workspace",
  "portsAttributes": {
    "9000": {
      "label": "application",
      "onAutoForward": "silent",
      "protocol": "http"
    }
  },
  "forwardPorts": [9000],
  "remoteUser": "root",
  "customizations": {
    "vscode": {
      "extensions": [
        "SonarSource.sonarlint-vscode",
        "AbhijoyBasak.nestjs-files",
        "ashinzekene.nestjs",
        "MariusAlchimavicius.json-to-ts"
      ]
    }
  },
}
