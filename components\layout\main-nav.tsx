"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"

import { cn } from "@/lib/utils"
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu"
import { Icons } from "@/components/icons"

interface MainNavProps extends React.HTMLAttributes<HTMLElement> {
  userRole?: string
}

export function MainNav({ className, userRole, ...props }: MainNavProps) {
  const pathname = usePathname()

  return (
    <NavigationMenu className={cn("hidden md:flex", className)} {...props}>
      <NavigationMenuList>
        <NavigationMenuItem>
          <Link href={userRole === "medical_staff" ? "/doctor/dashboard" : "/dashboard"} legacyBehavior passHref>
            <NavigationMenuLink
              className={cn(
                navigationMenuTriggerStyle(),
                (pathname === "/dashboard" || pathname === "/doctor/dashboard") && "bg-primary/10 text-primary"
              )}
            >
              <Icons.dashboard className="mr-2 h-4 w-4" />
              Dashboard
            </NavigationMenuLink>
          </Link>
        </NavigationMenuItem>

        {/* Navigation for medical staff */}
        {userRole === "medical_staff" && (
          <>
            <NavigationMenuItem>
              <NavigationMenuTrigger
                className={cn(
                  pathname?.startsWith("/doctor/patient") && "bg-primary/10 text-primary"
                )}
              >
                <Icons.users className="mr-2 h-4 w-4" />
                Bệnh nhân
              </NavigationMenuTrigger>
              <NavigationMenuContent>
                <ul className="grid gap-3 p-6 md:w-[400px] lg:w-[500px] lg:grid-cols-2">
                  <ListItem href="/doctor/patient-links" title="Quản lý bệnh nhân">
                    Danh sách bệnh nhân đã liên kết
                  </ListItem>
                  <ListItem href="/shared-records" title="Hồ sơ được chia sẻ">
                    Xem hồ sơ y tế được chia sẻ
                  </ListItem>
                </ul>
              </NavigationMenuContent>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <NavigationMenuTrigger
                className={cn(
                  (pathname?.startsWith("/consultations") || pathname?.startsWith("/advisory")) && "bg-primary/10 text-primary"
                )}
              >
                <Icons.calendar className="mr-2 h-4 w-4" />
                Tư vấn
              </NavigationMenuTrigger>
              <NavigationMenuContent>
                <ul className="grid gap-3 p-6 md:w-[400px] lg:w-[500px] lg:grid-cols-2">
                  <ListItem href="/consultations/doctor" title="Lịch tư vấn">
                    Quản lý lịch tư vấn của bạn
                  </ListItem>
                  <ListItem href="/advisory" title="Yêu cầu tư vấn">
                    Xử lý yêu cầu tư vấn từ bệnh nhân
                  </ListItem>
                  <ListItem href="/consultations/staff-request" title="Hội chẩn y khoa">
                    Tạo và tham gia hội chẩn
                  </ListItem>
                </ul>
              </NavigationMenuContent>
            </NavigationMenuItem>
          </>
        )}

        {/* Navigation for patients */}
        {userRole === "patient" && (
          <NavigationMenuItem>
            <NavigationMenuTrigger
              className={cn(
                pathname?.startsWith("/medical-records") ||
                pathname?.startsWith("/prescriptions") ||
                pathname?.startsWith("/health-metrics") ||
                (pathname?.startsWith("/symptoms") && "bg-primary/10 text-primary"),
              )}
            >
              <Icons.medical className="mr-2 h-4 w-4" />
              Hồ sơ y tế
            </NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className="grid gap-3 p-6 md:w-[400px] lg:w-[500px] lg:grid-cols-2">
              <li className="row-span-3">
                <NavigationMenuLink asChild>
                  <a
                    className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-teal-500 to-teal-700 p-6 no-underline outline-none focus:shadow-md"
                    href="/medical-records"
                  >
                    <Icons.fileText className="h-6 w-6 text-white" />
                    <div className="mt-4 mb-2 text-lg font-medium text-white">Hồ sơ y tế</div>
                    <p className="text-sm leading-tight text-white/90">
                      Quản lý hồ sơ y tế, lịch sử bệnh án và tài liệu y tế của bạn
                    </p>
                  </a>
                </NavigationMenuLink>
              </li>
              <ListItem href="/prescriptions" title="Đơn thuốc">
                Quản lý đơn thuốc và lịch uống thuốc
              </ListItem>
              <ListItem href="/health-metrics" title="Chỉ số sức khỏe">
                Theo dõi các chỉ số sức khỏe quan trọng
              </ListItem>
              <ListItem href="/symptoms" title="Triệu chứng">
                Ghi lại và theo dõi các triệu chứng
              </ListItem>
            </ul>
          </NavigationMenuContent>
          </NavigationMenuItem>
          <NavigationMenuItem>
            <NavigationMenuTrigger
              className={cn(
                pathname?.startsWith("/consultations") ||
                (pathname?.startsWith("/appointments") && "bg-primary/10 text-primary"),
              )}
            >
              <Icons.calendar className="mr-2 h-4 w-4" />
              Cuộc hẹn
            </NavigationMenuTrigger>
            <NavigationMenuContent>
              <ul className="grid gap-3 p-6 md:w-[400px] lg:w-[500px] lg:grid-cols-2">
                <ListItem href="/consultations" title="Tư vấn">
                  Đặt lịch và quản lý các buổi tư vấn
                </ListItem>
                <ListItem href="/consultations/request" title="Yêu cầu tư vấn">
                  Gửi yêu cầu tư vấn mới
                </ListItem>
                <ListItem href="/appointments/reminders" title="Nhắc nhở lịch hẹn">
                  Quản lý nhắc nhở cho các cuộc hẹn
                </ListItem>
              </ul>
            </NavigationMenuContent>
          </NavigationMenuItem>
        )}
        <NavigationMenuItem>
          <Link href="/knowledge" legacyBehavior passHref>
            <NavigationMenuLink
              className={cn(
                navigationMenuTriggerStyle(),
                pathname?.startsWith("/knowledge") && "bg-primary/10 text-primary",
              )}
            >
              <Icons.book className="mr-2 h-4 w-4" />
              Thư viện kiến thức
            </NavigationMenuLink>
          </Link>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <Link href="/messages" legacyBehavior passHref>
            <NavigationMenuLink
              className={cn(
                navigationMenuTriggerStyle(),
                pathname?.startsWith("/messages") && "bg-primary/10 text-primary",
              )}
            >
              <Icons.message className="mr-2 h-4 w-4" />
              Tin nhắn
            </NavigationMenuLink>
          </Link>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <Link href="/connect" legacyBehavior passHref>
            <NavigationMenuLink
              className={cn(
                navigationMenuTriggerStyle(),
                pathname?.startsWith("/connect") && "bg-primary/10 text-primary",
              )}
            >
              <Icons.users className="mr-2 h-4 w-4" />
              Cộng đồng
            </NavigationMenuLink>
          </Link>
        </NavigationMenuItem>
        {userRole === "admin" && (
          <NavigationMenuItem>
            <Link href="/admin" legacyBehavior passHref>
              <NavigationMenuLink
                className={cn(
                  navigationMenuTriggerStyle(),
                  pathname?.startsWith("/admin") && "bg-primary/10 text-primary",
                )}
              >
                <Icons.settings className="mr-2 h-4 w-4" />
                Quản trị
              </NavigationMenuLink>
            </Link>
          </NavigationMenuItem>
        )}
      </NavigationMenuList>
    </NavigationMenu>
  )
}

const ListItem = React.forwardRef<React.ElementRef<"a">, React.ComponentPropsWithoutRef<"a">>(
  ({ className, title, children, ...props }, ref) => {
    return (
      <li>
        <NavigationMenuLink asChild>
          <a
            ref={ref}
            className={cn(
              "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
              className,
            )}
            {...props}
          >
            <div className="text-sm font-medium leading-none">{title}</div>
            <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">{children}</p>
          </a>
        </NavigationMenuLink>
      </li>
    )
  },
)
ListItem.displayName = "ListItem"
