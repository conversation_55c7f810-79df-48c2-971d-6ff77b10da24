import { redirect } from "next/navigation"
import { createClient } from "@/lib/supabase/server"
import { AuthForm } from "@/components/auth/auth-form"
import type { Metadata } from "next"

export const metadata: Metadata = {
  title: "<PERSON><PERSON>ng nhập | OncoCare.VN",
  description: "Đ<PERSON>ng nhập vào nền tảng OncoCare.VN để quản lý hồ sơ y tế và tham gia tư vấn trực tuyến",
}

export default async function LoginPage() {
  const supabase = createClient()

  const {
    data: { session },
  } = await supabase.auth.getSession()

  if (session) {
    redirect("/dashboard")
  }

  return <AuthForm />
}
