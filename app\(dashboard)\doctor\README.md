# Dashboard Bác sĩ - OncoCare.VN

## Tổng quan

Dashboard bác sĩ được thiết kế để cung cấp một giao diện tổng quan và quản lý hiệu quả cho nhân viên y tế trong hệ thống OncoCare.VN.

## Cấu trúc

### 1. Trang Dashboard Chính (`/doctor/dashboard`)

**Tính năng chính:**
- **Thống kê tổng quan**: Hi<PERSON><PERSON> thị số liệu quan trọng như tổng số bệnh nhân, lịch tư vấn hôm nay, yê<PERSON> cầu chờ xử lý
- **Quick Links**: <PERSON><PERSON><PERSON> cập nhanh đến các chức năng quan trọng
- **Bệnh nhân gần đây**: Danh sách 5 bệnh nh<PERSON> được truy cập gần đây nhất
- **Lị<PERSON> tư vấn sắp tới**: 5 buổi tư vấn sắp diễn ra

**Tabs:**
- **Tổng quan**: <PERSON><PERSON><PERSON> thị thống kê và hoạt động gần đây
- **Bệnh nhân**: Quản lý danh sách bệnh nhân
- **Tư vấn**: Quản lý lịch tư vấn và hội chẩn

### 2. Components

#### DoctorStatsCards (`components/doctor/doctor-stats-cards.tsx`)
Hiển thị 6 thẻ thống kê:
- Tổng bệnh nhân đang theo dõi
- Tư vấn hôm nay
- Yêu cầu chờ xử lý
- Tư vấn tuần này
- Hội chẩn chờ
- Hoạt động gần đây

#### DoctorQuickLinks (`components/doctor/doctor-quick-links.tsx`)
6 liên kết truy cập nhanh:
- Quản lý bệnh nhân
- Lịch tư vấn
- Yêu cầu tư vấn
- Hội chẩn y khoa
- Hồ sơ được chia sẻ
- Nhắn tin

#### RecentPatients (`components/doctor/recent-patients.tsx`)
- Hiển thị danh sách bệnh nhân đã liên kết
- Thông tin cơ bản: tên, chẩn đoán, trạng thái điều trị
- Thời gian liên kết và truy cập gần nhất
- Nút truy cập nhanh: nhắn tin và xem chi tiết

#### UpcomingConsultations (`components/doctor/upcoming-consultations.tsx`)
- Danh sách lịch tư vấn sắp tới
- Thông tin bệnh nhân và thời gian
- Mức độ ưu tiên và trạng thái
- Nút truy cập: nhắn tin, video call, xem chi tiết

## Routing và Navigation

### Automatic Redirect
- Khi bác sĩ truy cập `/dashboard`, hệ thống tự động chuyển hướng đến `/doctor/dashboard`
- Logic redirect được xử lý trong `app/(dashboard)/dashboard/page.tsx`

### Navigation Updates
- **Main Navigation**: Cập nhật để hiển thị menu riêng cho medical_staff
- **Mobile Navigation**: Điều chỉnh routes và labels phù hợp với vai trò bác sĩ

### Navigation Structure cho Medical Staff:
- **Dashboard**: `/doctor/dashboard`
- **Bệnh nhân**: 
  - Quản lý bệnh nhân: `/doctor/patient-links`
  - Hồ sơ được chia sẻ: `/shared-records`
- **Tư vấn**:
  - Lịch tư vấn: `/consultations/doctor`
  - Yêu cầu tư vấn: `/advisory`
  - Hội chẩn y khoa: `/consultations/staff-request`

## Database Integration

### Bảng liên quan:
- `users`: Thông tin cơ bản người dùng
- `medical_staff`: Thông tin chuyên môn bác sĩ
- `doctor_patient_links`: Liên kết bác sĩ - bệnh nhân
- `patient_advisories`: Yêu cầu tư vấn từ bệnh nhân
- `advisory_assignments`: Phân công tư vấn cho bác sĩ
- `staff_consultations`: Hội chẩn y khoa
- `medical_record_access_logs`: Lịch sử truy cập hồ sơ

### Queries chính:
- Thống kê số lượng bệnh nhân, tư vấn, yêu cầu
- Danh sách bệnh nhân đã liên kết với thông tin cơ bản
- Lịch tư vấn được phân công cho bác sĩ
- Hoạt động truy cập gần đây

## Responsive Design

Dashboard được thiết kế responsive với:
- **Desktop**: Layout 3 cột cho thống kê, 2 cột cho recent activities
- **Tablet**: Layout 2 cột cho thống kê, 1 cột cho activities
- **Mobile**: Layout 1 cột, sử dụng mobile navigation

## Security & Permissions

- Chỉ user có role `medical_staff` mới được truy cập
- Kiểm tra authentication và authorization ở mọi component
- RLS (Row Level Security) được áp dụng cho tất cả queries database

## Future Enhancements

1. **Real-time Updates**: WebSocket cho thông báo real-time
2. **Advanced Analytics**: Biểu đồ và báo cáo chi tiết
3. **Calendar Integration**: Tích hợp lịch Google/Outlook
4. **Mobile App**: PWA cho mobile experience tốt hơn
5. **AI Assistant**: Chatbot hỗ trợ bác sĩ

## Testing

Để test dashboard:
1. Đăng nhập với tài khoản có role `medical_staff`
2. Truy cập `/dashboard` (sẽ redirect đến `/doctor/dashboard`)
3. Kiểm tra các thống kê hiển thị đúng
4. Test các quick links và navigation
5. Kiểm tra responsive trên các thiết bị khác nhau
