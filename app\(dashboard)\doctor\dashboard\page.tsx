"use client"

import { useEffect, useState } from "react"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import type { Database } from "@/types/supabase"
import { redirect } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2 } from "lucide-react"
import { DoctorStatsCards } from "@/components/doctor/doctor-stats-cards"
import { DoctorQuickLinks } from "@/components/doctor/doctor-quick-links"
import { RecentPatients } from "@/components/doctor/recent-patients"
import { UpcomingConsultations } from "@/components/doctor/upcoming-consultations"
import { DoctorProgress } from "@/components/doctor/doctor-progress"

interface UserData {
  id: string
  email: string
  full_name: string
  role: string
  phone_number?: string
  date_of_birth?: string
  gender?: string
  address?: string
  avatar_url?: string
  created_at: string
  updated_at: string
}

interface MedicalStaffData {
  id: string
  specialization: string
  license_number: string
  hospital?: string
  department?: string
  years_of_experience?: number
  education?: string
  bio?: string
  is_verified: boolean
}

export default function DoctorDashboardPage() {
  const [userData, setUserData] = useState<UserData | null>(null)
  const [medicalStaffData, setMedicalStaffData] = useState<MedicalStaffData | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClientComponentClient<Database>()

  useEffect(() => {
    async function loadUserData() {
      try {
        // Kiểm tra phiên đăng nhập
        const {
          data: { session },
        } = await supabase.auth.getSession()

        if (!session) {
          redirect("/login")
          return
        }

        // Lấy thông tin người dùng
        const { data: user, error: userError } = await supabase
          .from("users")
          .select("*")
          .eq("id", session.user.id)
          .single()

        if (userError) {
          console.error("Error fetching user:", userError)
          return
        }

        if (!user) {
          redirect("/login")
          return
        }

        // Kiểm tra role - chỉ cho phép medical_staff
        if (user.role !== "medical_staff") {
          redirect("/dashboard")
          return
        }

        setUserData(user)

        // Lấy thông tin medical staff
        const { data: medicalStaff, error: medicalStaffError } = await supabase
          .from("medical_staff")
          .select("*")
          .eq("id", session.user.id)
          .single()

        if (medicalStaffError) {
          console.error("Error fetching medical staff data:", medicalStaffError)
        } else {
          setMedicalStaffData(medicalStaff)
        }
      } catch (error) {
        console.error("Error loading user data:", error)
      } finally {
        setLoading(false)
      }
    }

    loadUserData()
  }, [supabase])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!userData) {
    redirect("/login")
    return null
  }

  return (
    <div className="container mx-auto py-4">
      {/* Welcome section */}
      <div className="mb-6 flex flex-col space-y-2">
        <h1 className="text-2xl font-bold">Xin chào, BS. {userData.full_name}</h1>
        <p className="text-muted-foreground">
          Chào mừng bạn quay trở lại với OncoCare.VN - Dashboard Bác sĩ
        </p>
        {medicalStaffData && (
          <p className="text-sm text-muted-foreground">
            Chuyên khoa: {medicalStaffData.specialization}
            {medicalStaffData.hospital && ` • ${medicalStaffData.hospital}`}
            {medicalStaffData.department && ` • ${medicalStaffData.department}`}
          </p>
        )}
      </div>

      {/* Main dashboard content with tabs */}
      <Tabs defaultValue="overview" className="mb-8">
        <TabsList className="mb-4 grid w-full grid-cols-3 md:w-auto">
          <TabsTrigger value="overview">Tổng quan</TabsTrigger>
          <TabsTrigger value="patients">Bệnh nhân</TabsTrigger>
          <TabsTrigger value="consultations">Tư vấn</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          {/* Doctor summary - Similar to patient info */}
          {medicalStaffData && (
            <Card className="border-l-4 border-l-blue-500">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Thông tin bác sĩ</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <div>
                    <p className="text-sm font-medium">Chuyên khoa</p>
                    <p className="text-sm text-muted-foreground">
                      {medicalStaffData.specialization}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Bệnh viện</p>
                    <p className="text-sm text-muted-foreground">
                      {medicalStaffData.hospital || "Chưa có thông tin"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Khoa</p>
                    <p className="text-sm text-muted-foreground">
                      {medicalStaffData.department || "Chưa có thông tin"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Kinh nghiệm</p>
                    <p className="text-sm text-muted-foreground">
                      {medicalStaffData.years_of_experience ? `${medicalStaffData.years_of_experience} năm` : "Chưa có thông tin"}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Statistics Cards */}
          <DoctorStatsCards doctorId={userData.id} />

          {/* Progress Section */}
          <DoctorProgress doctorId={userData.id} />

          {/* Recent Activity Grid */}
          <div className="grid gap-6 md:grid-cols-2">
            <RecentPatients doctorId={userData.id} />
            <UpcomingConsultations doctorId={userData.id} />
          </div>

          {/* Quick Access Section - Similar to patient dashboard */}
          <div className="mb-6">
            <h2 className="mb-4 text-xl font-bold">Truy cập nhanh</h2>
            <DoctorQuickLinks />
          </div>
        </TabsContent>

        {/* Patients Tab */}
        <TabsContent value="patients" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Quản lý bệnh nhân</CardTitle>
              <CardDescription>
                Danh sách bệnh nhân đang theo dõi và quản lý
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RecentPatients doctorId={userData.id} showAll={true} />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Consultations Tab */}
        <TabsContent value="consultations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Lịch tư vấn</CardTitle>
              <CardDescription>
                Quản lý lịch tư vấn và hội chẩn y khoa
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UpcomingConsultations doctorId={userData.id} showAll={true} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
