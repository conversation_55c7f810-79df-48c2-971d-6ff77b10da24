"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { Home, Users, Calendar, User, MessageCircle, Settings, BookOpen, FileText } from "lucide-react"
import { cn } from "@/lib/utils"
import type { UserRole } from "@/types"
import { motion } from "framer-motion"

interface MobileNavProps {
  userRole?: UserRole
}

export function MobileNav({ userRole }: MobileNavProps) {
  const pathname = usePathname()

  // Xác định nếu đang ở trang Connect
  const isConnectPage = pathname.startsWith("/connect")

  // Các route cho trang Connect
  const connectRoutes = [
    {
      href: "/connect",
      label: "Bảng tin",
      icon: Home,
      active: pathname === "/connect",
    },
    {
      href: "/connect/groups",
      label: "Nhóm",
      icon: Users,
      active: pathname.startsWith("/connect/groups"),
    },
    {
      href: "/connect/events",
      label: "Sự kiện",
      icon: Calendar,
      active: pathname.startsWith("/connect/events"),
    },
    {
      href: "/connect/profile",
      label: "<PERSON><PERSON> sơ",
      icon: User,
      active: pathname === "/connect/profile",
    },
    {
      href: "/messages",
      label: "Tin nhắn",
      icon: MessageCircle,
      active: pathname.startsWith("/messages"),
    },
  ]

  // Các route chính
  const mainRoutes = [
    {
      href: userRole === "medical_staff" ? "/doctor/dashboard" : "/dashboard",
      label: "Tổng quan",
      icon: Home,
      active: pathname === "/dashboard" || pathname === "/doctor/dashboard",
      roles: ["admin", "patient", "medical_staff"],
    },
    {
      href: userRole === "medical_staff" ? "/doctor/patient-links" : "/medical-records",
      label: userRole === "medical_staff" ? "Bệnh nhân" : "Hồ sơ",
      icon: userRole === "medical_staff" ? Users : FileText,
      active: userRole === "medical_staff"
        ? pathname.startsWith("/doctor/patient") || pathname.startsWith("/shared-records")
        : pathname.startsWith("/medical-records") ||
        pathname.startsWith("/prescriptions") ||
        pathname.startsWith("/health-metrics") ||
        pathname.startsWith("/symptoms"),
      roles: ["admin", "patient", "medical_staff"],
    },
    {
      href: userRole === "medical_staff" ? "/consultations/doctor" : "/consultations",
      label: "Tư vấn",
      icon: Calendar,
      active: pathname.startsWith("/consultations") || pathname.startsWith("/appointments") || pathname.startsWith("/advisory"),
      roles: ["admin", "patient", "medical_staff"],
    },
    {
      href: "/knowledge",
      label: "Kiến thức",
      icon: BookOpen,
      active: pathname.startsWith("/knowledge"),
      roles: ["admin", "patient", "medical_staff"],
    },
    {
      href: "/messages",
      label: "Tin nhắn",
      icon: MessageCircle,
      active: pathname.startsWith("/messages"),
      roles: ["admin", "patient", "medical_staff"],
    },
    {
      href: "/connect",
      label: "Cộng đồng",
      icon: Users,
      active: pathname.startsWith("/connect"),
      roles: ["admin", "patient", "medical_staff"],
    },
    {
      href: "/admin",
      label: "Quản trị",
      icon: Settings,
      active: pathname.startsWith("/admin"),
      roles: ["admin"],
    },
  ]

  const filteredMainRoutes = userRole ? mainRoutes.filter((route) => route.roles.includes(userRole)) : mainRoutes

  // Hiển thị thanh điều hướng phù hợp với trang hiện tại
  const routes = isConnectPage ? connectRoutes : filteredMainRoutes

  return (
    <div className="mobile-nav-container md:hidden fixed bottom-0 left-0 right-0 z-50 bg-background border-t">
      <nav className="mobile-nav grid grid-cols-5 py-2">
        {routes.slice(0, 5).map((route) => (
          <Link
            key={route.href}
            href={route.href}
            className={cn(
              "flex flex-col items-center justify-center p-2 rounded-md transition-colors",
              route.active ? "text-primary" : "text-muted-foreground hover:text-foreground",
            )}
          >
            {route.active ? (
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.2 }}
              >
                <route.icon className="h-5 w-5" />
              </motion.div>
            ) : (
              <route.icon className="h-5 w-5" />
            )}
            <span className="text-xs mt-1">{route.label}</span>
            {route.active && (
              <motion.div
                className="absolute bottom-1 h-1 w-10 bg-primary rounded-full"
                layoutId="activeTab"
                transition={{ type: "spring", stiffness: 300, damping: 30 }}
              />
            )}
          </Link>
        ))}
      </nav>
    </div>
  )
}
